import { useState } from 'react'
import './App.css'
import ParentComponent from './components/ParentComponent'
import TraditionalDemo from './components/TraditionalDemo'

function App() {
  const [globalCounter, setGlobalCounter] = useState(0)
  const [showTraditional, setShowTraditional] = useState(false)

  // 生成随机颜色
  const getRandomColor = () => {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  const appColor = getRandomColor()

  if (showTraditional) {
    return (
      <div>
        <div style={{ textAlign: 'center', padding: '10px', backgroundColor: '#2c3e50' }}>
          <button
            onClick={() => setShowTraditional(false)}
            style={{
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: '#3498db',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            切换到 React Compiler 优化版本
          </button>
        </div>
        <TraditionalDemo />
      </div>
    )
  }

  return (
    <div
      style={{
        padding: '20px',
        backgroundColor: appColor,
        minHeight: '100vh',
        borderRadius: '10px',
        margin: '10px'
      }}
    >
      <h1 style={{ color: 'white', textAlign: 'center', textShadow: '2px 2px 4px rgba(0,0,0,0.5)' }}>
        React 19 + Compiler 组件重绘演示
      </h1>
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <p style={{ color: 'white', fontSize: '16px' }}>
          App组件 - 全局计数器: {globalCounter}
        </p>
        <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button
            onClick={() => setGlobalCounter(prev => prev + 1)}
            style={{
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }}
          >
            增加全局计数器
          </button>
          <button
            onClick={() => setShowTraditional(true)}
            style={{
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: '#e74c3c',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }}
          >
            切换到传统版本 (看真正的重绘)
          </button>
        </div>
      </div>

      <ParentComponent globalCounter={globalCounter} />
    </div>
  )
}

export default App
