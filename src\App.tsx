import { useState } from 'react'
import './App.css'
import ParentComponent from './components/ParentComponent'

function App() {
  const [globalCounter, setGlobalCounter] = useState(0)

  // 生成随机颜色
  const getRandomColor = () => {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  const appColor = getRandomColor()

  return (
    <div
      style={{
        padding: '20px',
        backgroundColor: appColor,
        minHeight: '100vh',
        borderRadius: '10px',
        margin: '10px'
      }}
    >
      <h1 style={{ color: 'white', textAlign: 'center', textShadow: '2px 2px 4px rgba(0,0,0,0.5)' }}>
        React 19 组件重绘演示
      </h1>
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <p style={{ color: 'white', fontSize: '16px' }}>
          App组件 - 全局计数器: {globalCounter}
        </p>
        <button
          onClick={() => setGlobalCounter(prev => prev + 1)}
          style={{
            padding: '10px 20px',
            fontSize: '16px',
            backgroundColor: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
          }}
        >
          增加全局计数器 (触发App重绘)
        </button>
      </div>

      <ParentComponent globalCounter={globalCounter} />
    </div>
  )
}

export default App
