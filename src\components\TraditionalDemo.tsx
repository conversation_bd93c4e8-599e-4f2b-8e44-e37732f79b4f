import { useState } from 'react'

// 传统的未优化组件 - 故意不使用 memo
function TraditionalGrandChild({ 
  grandChildId, 
  childCounter, 
  parentCounter, 
  globalCounter 
}: {
  grandChildId: string
  childCounter: number
  parentCounter: number
  globalCounter: number
}) {
  const [grandChildCounter, setGrandChildCounter] = useState(0)
  
  // 每次渲染都生成新颜色，展示重绘
  const getRandomColor = () => {
    const colors = ['#fab1a0', '#00cec9', '#e17055', '#81ecec', '#a29bfe', '#fd79a8', '#fdcb6e', '#55a3ff']
    return colors[Math.floor(Math.random() * colors.length)]
  }
  
  const color = getRandomColor()
  
  // 记录渲染次数
  console.log(`🔄 TraditionalGrandChild ${grandChildId} 重新渲染`)
  
  return (
    <div style={{ 
      padding: '10px', 
      backgroundColor: color, 
      margin: '3px',
      borderRadius: '4px',
      border: '1px solid white',
      fontSize: '11px'
    }}>
      <h4 style={{ color: 'white', margin: '0 0 8px 0', fontSize: '13px' }}>
        传统孙子组件 {grandChildId}
      </h4>
      <div style={{ color: 'white', marginBottom: '8px' }}>
        <div>孙子计数: {grandChildCounter}</div>
        <div>子组件计数: {childCounter}</div>
        <div>父组件计数: {parentCounter}</div>
        <div>全局计数: {globalCounter}</div>
      </div>
      <button 
        onClick={() => setGrandChildCounter(prev => prev + 1)}
        style={{
          padding: '4px 8px',
          fontSize: '10px',
          backgroundColor: 'white',
          border: 'none',
          borderRadius: '2px',
          cursor: 'pointer'
        }}
      >
        +1 孙子{grandChildId}
      </button>
    </div>
  )
}

function TraditionalChild({ 
  childId, 
  parentCounter, 
  globalCounter 
}: {
  childId: string
  parentCounter: number
  globalCounter: number
}) {
  const [childCounter, setChildCounter] = useState(0)
  
  const getRandomColor = () => {
    const colors = ['#ff7675', '#74b9ff', '#00b894', '#fdcb6e', '#a29bfe', '#fd79a8', '#6c5ce7', '#ffeaa7']
    return colors[Math.floor(Math.random() * colors.length)]
  }
  
  const color = getRandomColor()
  
  // 记录渲染次数
  console.log(`🔄 TraditionalChild ${childId} 重新渲染`)
  
  return (
    <div style={{ 
      padding: '15px', 
      backgroundColor: color, 
      margin: '5px',
      borderRadius: '6px',
      border: '2px solid white'
    }}>
      <h3 style={{ color: 'white', margin: '0 0 10px 0' }}>
        传统子组件 {childId}
      </h3>
      <div style={{ marginBottom: '10px' }}>
        <p style={{ color: 'white', fontSize: '12px', margin: '5px 0' }}>
          子组件{childId}计数: {childCounter} | 父组件计数: {parentCounter} | 全局计数: {globalCounter}
        </p>
        <button 
          onClick={() => setChildCounter(prev => prev + 1)}
          style={{
            padding: '6px 12px',
            fontSize: '12px',
            backgroundColor: 'white',
            border: 'none',
            borderRadius: '3px',
            cursor: 'pointer',
            marginRight: '8px'
          }}
        >
          增加子组件{childId}计数
        </button>
      </div>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
        <TraditionalGrandChild 
          grandChildId={`${childId}-1`}
          childCounter={childCounter}
          parentCounter={parentCounter}
          globalCounter={globalCounter}
        />
        <TraditionalGrandChild 
          grandChildId={`${childId}-2`}
          childCounter={childCounter}
          parentCounter={parentCounter}
          globalCounter={globalCounter}
        />
      </div>
    </div>
  )
}

function TraditionalParent({ globalCounter }: { globalCounter: number }) {
  const [parentCounter, setParentCounter] = useState(0)
  
  const getRandomColor = () => {
    const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22']
    return colors[Math.floor(Math.random() * colors.length)]
  }
  
  const color = getRandomColor()
  
  // 记录渲染次数
  console.log(`🔄 TraditionalParent 重新渲染`)
  
  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: color, 
      margin: '10px',
      borderRadius: '8px',
      border: '3px solid white'
    }}>
      <h2 style={{ color: 'white' }}>传统父组件 (未优化)</h2>
      <div style={{ marginBottom: '15px' }}>
        <p style={{ color: 'white', fontSize: '14px' }}>
          父组件计数: {parentCounter} | 全局计数: {globalCounter}
        </p>
        <button 
          onClick={() => setParentCounter(prev => prev + 1)}
          style={{
            padding: '8px 16px',
            fontSize: '14px',
            backgroundColor: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          增加父组件计数
        </button>
      </div>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
        <TraditionalChild 
          childId="A"
          parentCounter={parentCounter}
          globalCounter={globalCounter}
        />
        <TraditionalChild 
          childId="B"
          parentCounter={parentCounter}
          globalCounter={globalCounter}
        />
      </div>
    </div>
  )
}

export default function TraditionalDemo() {
  const [globalCounter, setGlobalCounter] = useState(0)
  
  const getRandomColor = () => {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd']
    return colors[Math.floor(Math.random() * colors.length)]
  }
  
  const color = getRandomColor()
  
  // 记录渲染次数
  console.log(`🔄 TraditionalDemo App 重新渲染`)
  
  return (
    <div style={{ 
      padding: '20px', 
      backgroundColor: color, 
      minHeight: '100vh',
      borderRadius: '10px',
      margin: '10px'
    }}>
      <h1 style={{ color: 'white', textAlign: 'center' }}>
        传统 React 重绘演示 (未优化)
      </h1>
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <p style={{ color: 'white', fontSize: '16px' }}>
          全局计数: {globalCounter} - 打开控制台查看重绘日志
        </p>
        <button 
          onClick={() => setGlobalCounter(prev => prev + 1)}
          style={{
            padding: '10px 20px',
            fontSize: '16px',
            backgroundColor: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          增加全局计数 (所有组件都会重绘!)
        </button>
      </div>
      
      <TraditionalParent globalCounter={globalCounter} />
    </div>
  )
}
