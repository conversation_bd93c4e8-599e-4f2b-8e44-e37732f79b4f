body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

#root {
  min-height: 100vh;
}

/* 按钮悬停效果 */
button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
  transition: all 0.2s ease;
}

button:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* 组件容器动画 */
div[style*="backgroundColor"] {
  transition: all 0.3s ease;
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  div[style*="padding"] {
    padding: 10px !important;
    margin: 5px !important;
  }

  button {
    font-size: 12px !important;
    padding: 6px 10px !important;
  }

  h1 {
    font-size: 24px !important;
  }

  h2 {
    font-size: 20px !important;
  }

  h3 {
    font-size: 16px !important;
  }

  h4 {
    font-size: 14px !important;
  }
}
