import { useState } from 'react'
import ChildComponent from './ChildComponent'

interface ParentComponentProps {
  globalCounter: number
}

function ParentComponent({ globalCounter }: ParentComponentProps) {
  const [parentCounter, setParentCounter] = useState(0)
  const [showChildren, setShowChildren] = useState(true)

  // 生成随机颜色
  const getRandomColor = () => {
    const colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e', '#e67e22']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  const parentColor = getRandomColor()

  return (
    <div 
      style={{ 
        padding: '20px', 
        backgroundColor: parentColor, 
        margin: '10px',
        borderRadius: '8px',
        border: '3px solid white'
      }}
    >
      <h2 style={{ color: 'white', textShadow: '1px 1px 2px rgba(0,0,0,0.7)' }}>
        父组件 (ParentComponent)
      </h2>
      <div style={{ marginBottom: '15px' }}>
        <p style={{ color: 'white', fontSize: '14px' }}>
          父组件计数器: {parentCounter} | 接收到的全局计数器: {globalCounter}
        </p>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            onClick={() => setParentCounter(prev => prev + 1)}
            style={{
              padding: '8px 16px',
              fontSize: '14px',
              backgroundColor: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }}
          >
            增加父组件计数器
          </button>
          <button 
            onClick={() => setShowChildren(prev => !prev)}
            style={{
              padding: '8px 16px',
              fontSize: '14px',
              backgroundColor: showChildren ? '#e74c3c' : '#2ecc71',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              boxShadow: '0 2px 4px rgba(0,0,0,0.2)'
            }}
          >
            {showChildren ? '隐藏' : '显示'}子组件
          </button>
        </div>
      </div>

      {showChildren && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
          <ChildComponent 
            parentCounter={parentCounter} 
            globalCounter={globalCounter}
            childId="A"
          />
          <ChildComponent 
            parentCounter={parentCounter} 
            globalCounter={globalCounter}
            childId="B"
          />
        </div>
      )}
    </div>
  )
}

export default ParentComponent
