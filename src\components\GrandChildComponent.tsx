import { useState, useEffect } from 'react'

interface GrandChildComponentProps {
  childCounter: number
  parentCounter: number
  globalCounter: number
  grandChildId: string
}

function GrandChildComponent({ 
  childCounter, 
  parentCounter, 
  globalCounter, 
  grandChildId 
}: GrandChildComponentProps) {
  const [grandChildCounter, setGrandChildCounter] = useState(0)
  const [renderCount, setRenderCount] = useState(0)

  // 每次渲染时增加渲染计数
  useEffect(() => {
    setRenderCount(prev => prev + 1)
  })

  // 生成随机颜色
  const getRandomColor = () => {
    const colors = ['#fab1a0', '#00cec9', '#e17055', '#81ecec', '#a29bfe', '#fd79a8', '#fdcb6e', '#55a3ff']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  const grandChildColor = getRandomColor()

  // 计算总的影响因子（用于展示组件接收到的所有状态变化）
  const totalInfluence = grandChildCounter + childCounter + parentCounter + globalCounter

  return (
    <div 
      style={{ 
        padding: '10px', 
        backgroundColor: grandChildColor, 
        margin: '3px',
        borderRadius: '4px',
        border: '1px solid white',
        fontSize: '11px'
      }}
    >
      <h4 style={{ 
        color: 'white', 
        textShadow: '1px 1px 1px rgba(0,0,0,0.7)', 
        margin: '0 0 8px 0',
        fontSize: '13px'
      }}>
        孙子组件 {grandChildId} (GrandChildComponent)
      </h4>
      
      <div style={{ marginBottom: '8px' }}>
        <div style={{ color: 'white', marginBottom: '3px' }}>
          <strong>渲染次数: {renderCount}</strong>
        </div>
        <div style={{ color: 'white', marginBottom: '2px' }}>
          孙子计数器: {grandChildCounter}
        </div>
        <div style={{ color: 'white', marginBottom: '2px' }}>
          子组件计数器: {childCounter}
        </div>
        <div style={{ color: 'white', marginBottom: '2px' }}>
          父组件计数器: {parentCounter}
        </div>
        <div style={{ color: 'white', marginBottom: '2px' }}>
          全局计数器: {globalCounter}
        </div>
        <div style={{ color: 'white', fontWeight: 'bold', marginTop: '5px' }}>
          总影响因子: {totalInfluence}
        </div>
      </div>

      <div style={{ display: 'flex', gap: '5px', flexWrap: 'wrap' }}>
        <button 
          onClick={() => setGrandChildCounter(prev => prev + 1)}
          style={{
            padding: '4px 8px',
            fontSize: '10px',
            backgroundColor: 'white',
            border: 'none',
            borderRadius: '2px',
            cursor: 'pointer',
            boxShadow: '0 1px 2px rgba(0,0,0,0.2)'
          }}
        >
          +1 孙子{grandChildId}
        </button>
        <button 
          onClick={() => setGrandChildCounter(0)}
          style={{
            padding: '4px 8px',
            fontSize: '10px',
            backgroundColor: '#e74c3c',
            color: 'white',
            border: 'none',
            borderRadius: '2px',
            cursor: 'pointer',
            boxShadow: '0 1px 2px rgba(0,0,0,0.2)'
          }}
        >
          重置
        </button>
      </div>
    </div>
  )
}

export default GrandChildComponent
