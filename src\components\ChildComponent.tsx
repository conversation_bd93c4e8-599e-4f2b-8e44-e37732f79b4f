import { useState } from 'react'
import GrandChildComponent from './GrandChildComponent'

interface ChildComponentProps {
  parentCounter: number
  globalCounter: number
  childId: string
}

function ChildComponent({ parentCounter, globalCounter, childId }: ChildComponentProps) {
  const [childCounter, setChildCounter] = useState(0)
  const [isExpanded, setIsExpanded] = useState(true)

  // 生成随机颜色
  const getRandomColor = () => {
    const colors = ['#ff7675', '#74b9ff', '#00b894', '#fdcb6e', '#a29bfe', '#fd79a8', '#6c5ce7', '#ffeaa7']
    return colors[Math.floor(Math.random() * colors.length)]
  }

  const childColor = getRandomColor()

  return (
    <div 
      style={{ 
        padding: '15px', 
        backgroundColor: childColor, 
        margin: '5px',
        borderRadius: '6px',
        border: '2px solid white'
      }}
    >
      <h3 style={{ color: 'white', textShadow: '1px 1px 2px rgba(0,0,0,0.7)', margin: '0 0 10px 0' }}>
        子组件 {childId} (ChildComponent)
      </h3>
      <div style={{ marginBottom: '10px' }}>
        <p style={{ color: 'white', fontSize: '12px', margin: '5px 0' }}>
          子组件{childId}计数器: {childCounter}
        </p>
        <p style={{ color: 'white', fontSize: '12px', margin: '5px 0' }}>
          父组件计数器: {parentCounter} | 全局计数器: {globalCounter}
        </p>
        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
          <button 
            onClick={() => setChildCounter(prev => prev + 1)}
            style={{
              padding: '6px 12px',
              fontSize: '12px',
              backgroundColor: 'white',
              border: 'none',
              borderRadius: '3px',
              cursor: 'pointer',
              boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
            }}
          >
            增加子组件{childId}计数器
          </button>
          <button 
            onClick={() => setIsExpanded(prev => !prev)}
            style={{
              padding: '6px 12px',
              fontSize: '12px',
              backgroundColor: isExpanded ? '#e17055' : '#00b894',
              color: 'white',
              border: 'none',
              borderRadius: '3px',
              cursor: 'pointer',
              boxShadow: '0 1px 3px rgba(0,0,0,0.2)'
            }}
          >
            {isExpanded ? '收起' : '展开'}
          </button>
        </div>
      </div>

      {isExpanded && (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '5px' }}>
          <GrandChildComponent 
            childCounter={childCounter}
            parentCounter={parentCounter} 
            globalCounter={globalCounter}
            grandChildId={`${childId}-1`}
          />
          <GrandChildComponent 
            childCounter={childCounter}
            parentCounter={parentCounter} 
            globalCounter={globalCounter}
            grandChildId={`${childId}-2`}
          />
        </div>
      )}
    </div>
  )
}

export default ChildComponent
